<?php
get_header();

// Haal alle ACF velden op voor deze artiest
$artist_id = get_the_ID();

// Artist header block
get_template_part('blocks/artist/artist-header-block', null, ['artist_id' => $artist_id]);

// Two column text block (description)
get_template_part('blocks/artist/artist-two-column-text-block', null, ['artist_id' => $artist_id]);

// Latest release block (Spotify)
get_template_part('blocks/artist/artist-latest-release-block', null, ['artist_id' => $artist_id]);

// Big slider block (gallery)
get_template_part('blocks/artist/artist-big-slider-block', null, ['artist_id' => $artist_id]);

// Media block (YouTube & Spotify)
get_template_part('blocks/artist/artist-media-block', null, ['artist_id' => $artist_id]);

// Upcoming shows block (Bandsintown)
get_template_part('blocks/artist/artist-upcoming-shows-block', null, ['artist_id' => $artist_id]);

// Related artists block
get_template_part('blocks/artist/artist-related-artists-block', null, ['artist_id' => $artist_id]);

get_footer();
