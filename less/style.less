// APEX font-face declaraties
@font-face {
  font-family: 'ApexMk2-BoldExtended';
  src: url('./assets/fonts/ApexMk2-BoldExtended.woff2') format('woff2'),
       url('./assets/fonts/ApexMk2-BoldExtended.woff') format('woff'),
       url('./assets/fonts/ApexMk2-BoldExtended.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'ApexMk2-LightCondensed';
  src: url('./assets/fonts/ApexMk2-LightCondensed.woff2') format('woff2'),
       url('./assets/fonts/ApexMk2-LightCondensed.woff') format('woff'),
       url('./assets/fonts/ApexMk2-LightCondensed.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'ApexMk2-Regular';
  src: url('./assets/fonts/apexmk2-regular-webfont.woff2') format('woff2'),
       url('./assets/fonts/ApexMk2-Regular.woff2') format('woff2'),
       url('./assets/fonts/ApexMk2-Regular.woff') format('woff'),
       url('./assets/fonts/ApexMk2-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// Titels standaard APEX font
h1, h2, h3, h4, h5, h6, .bigTitle, .tinyTitle, .quickArtistsTitle, .subTitle {
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: bold;
  letter-spacing: 0.01em;
}

// Optioneel: andere tekststijlen
body, p, .text, .content {
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
}