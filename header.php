<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:type" content="image/jpeg" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />
  <meta name="DC.title" content="<?php echo the_title(); ?>">
  <meta name="DC.creator" content="Door Dennis">
  <meta name="DC.subject" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.publisher" content="<?php echo get_bloginfo('name'); ?>">
  <meta name="DC.language" content="nl">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta name="twitter:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="twitter:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
  <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://use.typekit.net/sui0kvj.css">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
    "image": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-featured-image')); ?>",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service",
      "contactOption": "TollFree",
      "areaServed": "NL",
      "availableLanguage": "Dutch"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>"
    },
    "sameAs": [
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
    ],
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Company Information",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-company-information')); ?>"
      },
      {
        "@type": "PropertyValue",
        "name": "Analytics ID",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-analytics')); ?>"
      }
    ]
  }
  </script>

  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
      'anonymize_ip': true
    });
  </script>

  <?php wp_head(); ?>
</head>
<?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>
  
<?php } ?>
  <body class="no-scroll">
    <header>
      <div class="contentWrapper smaller">
        <div class="col small">
            <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
             <img class="logoImage" src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo-white'))); ?>" alt="<?php the_title(); ?>"/>
            </a>
        </div>
        <div class="col">
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                'menu' => 'primary-menu',
              ) ); ?>
              <div class="button">
                <a class="innerText" href="/booking-request" title="Booking Request">Booking Request</a>
                <div id="burger" class="arrows">
                  <div class="hamburger"><span class="border"></span><span class="border"></span><span class="border"></span></div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </header>
    <div class="blurredOverlay"></div>
    <div id="menu" class="menu">
      <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
        <img class="logoImage" src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo'))); ?>" alt="<?php the_title(); ?>"/>
      </a>
      <div class="hamburger"><span class="border"></span><span class="border"></span><span class="border"></span></div>
      <div class="innerContent">
        <?php wp_nav_menu( array(
          'menu' => 'primary-menu',
        ) ); ?>
        <div class="divider"></div>
        <?php wp_nav_menu( array(
          'menu' => 'secondary-menu',
        ) ); ?>
      </div>
      <div class="socials">
        <?php if(get_theme_mod('customTheme-main-callout-instagram')): ?>
          <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-instagram"></i></a>
        <?php endif;
        if(get_theme_mod('customTheme-main-callout-tiktok')): ?>
          <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a>
        <?php endif;
        if(get_theme_mod('customTheme-main-callout-linkedin')): ?>
          <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin', 'https://www.linkedin.com')); ?>" title="linkedin" target="_blank"><i class="icon-linkedin"></i></a>
        <?php endif;
        if(get_theme_mod('customTheme-main-callout-facebook')): ?>
          <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook', 'https://www.facebook.com')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a>
        <?php endif; ?>
      </div>
      <div class="contactLinks">
        <a class="contactLink" href="tel:<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>" title="Call us"><i class="icon-phone"></i>&nbsp;<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone-label')); ?></a>
        <a class="contactLink" href="mailto:<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>" title="Mail us"><i class="icon-mail"></i>&nbsp;<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?></a>
      </div>
    </div>
    <div class="mainCursor"></div>
    <div id="pageContainer" class="transition-fade blocks<? if (is_singular('artist')) { echo ' artistPage'; } ?>">
      <?php
      // --- Helper: Remove empty/null values recursively for JSON-LD ---
      if (!function_exists('remove_empty')) {
        function remove_empty($arr) {
          foreach ($arr as $k => $v) {
            if (is_array($v)) {
              $arr[$k] = remove_empty($v);
              if (empty($arr[$k])) unset($arr[$k]);
            } elseif ($v === null || $v === '' || $v === []) {
              unset($arr[$k]);
            }
          }
          return $arr;
        }
      }
      // --- ARTIST JSON-LD STRUCTURED DATA ---
      if (is_singular('artist')) {
        $artist_id = get_the_ID();
        $artist_name = get_the_title($artist_id);
        $desc = get_field('description', $artist_id);
        $image = get_field('image', $artist_id);
        $image_url = is_array($image) ? $image['url'] : '';
        $logo = get_field('logo', $artist_id);
        $logo_url = is_array($logo) ? $logo['url'] : '';
        $category = get_field('category', $artist_id);
        $presskit = get_field('presskit', $artist_id);
        $visual_pack = get_field('visual_pack', $artist_id);
        $spotify = get_field('spotify', $artist_id);
        $soundcloud = get_field('soundcloud', $artist_id);
        $youtube = get_field('youtube', $artist_id);
        $facebook = get_field('facebook', $artist_id);
        $instagram = get_field('instagram', $artist_id);
        $tiktok = get_field('tiktok', $artist_id);
        $twitter_x = get_field('twitter_x', $artist_id);
        $website = get_field('website', $artist_id);
        $roster_type = get_field('roster_type', $artist_id);
        $artist_managers = get_field('artist_managers', $artist_id);
        $sameAs = array_filter([
          $spotify,
          $soundcloud,
          $youtube,
          $facebook,
          $instagram,
          $tiktok,
          $twitter_x,
          $website
        ]);
        $manager_emails = [];
        if ($artist_managers) {
          foreach ((array)$artist_managers as $manager_id) {
            $email = get_field('email', $manager_id);
            if ($email) $manager_emails[] = $email;
          }
        }
        $jsonld = [
          "@context" => "https://schema.org",
          "@type" => "MusicGroup",
          "name" => $artist_name,
          "description" => $desc,
          "image" => $image_url,
          "logo" => $logo_url,
          "genre" => $category,
          "url" => get_permalink($artist_id),
          "sameAs" => array_values($sameAs),
          "memberOf" => $roster_type ? ucfirst(str_replace('_', ' ', $roster_type)) : null,
          "email" => $manager_emails,
          "potentialAction" => [
            [
              "@type" => "ListenAction",
              "target" => $spotify ?: $soundcloud ?: $youtube ?: $website
            ],
            $presskit ? [
              "@type" => "ViewAction",
              "target" => $presskit
            ] : null,
            $visual_pack ? [
              "@type" => "ViewAction",
              "target" => $visual_pack
            ] : null
          ]
        ];
        $jsonld = remove_empty($jsonld);
        echo '<script type="application/ld+json">' . json_encode($jsonld, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
      }
      ?>
      <?php
      // --- JSON-LD STRUCTURED DATA VOOR ALLE PAGINA'S ---
      if (is_singular('artist')) {
        // ...bestaande artiesten MusicGroup JSON-LD code...
        // (deze blijft staan, zie vorige stap)
      } elseif (is_singular('team')) {
        // Teamleden als Person
        $team_id = get_the_ID();
        $name = get_the_title($team_id);
        $photo = get_field('photo', $team_id);
        $photo_url = is_array($photo) ? $photo['url'] : '';
        $category = get_field('category', $team_id);
        $phone = get_field('phone', $team_id);
        $email = get_field('email', $team_id);
        $whatsapp = get_field('whatsapp', $team_id);
        $instagram = get_field('instagram', $team_id);
        $tiktok = get_field('tiktok', $team_id);
        $facebook = get_field('facebook', $team_id);
        $linkedin = get_field('linkedin', $team_id);
        $website = get_field('website', $team_id);
        $sameAs = array_filter([
          $instagram, $tiktok, $facebook, $linkedin, $website, $whatsapp
        ]);
        $jsonld = [
          "@context" => "https://schema.org",
          "@type" => "Person",
          "name" => $name,
          "image" => $photo_url,
          "jobTitle" => $category,
          "email" => $email,
          "telephone" => $phone,
          "url" => get_permalink($team_id),
          "sameAs" => array_values($sameAs)
        ];
        $jsonld = remove_empty($jsonld);
        echo '<script type="application/ld+json">' . json_encode($jsonld, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
      } elseif (is_singular('post')) {
        // Blogpost/nieuws
        global $post;
        $author_id = $post->post_author;
        $author_name = get_the_author_meta('display_name', $author_id);
        $author_url = get_author_posts_url($author_id);
        $image = get_the_post_thumbnail_url($post->ID, 'full');
        $jsonld = [
          "@context" => "https://schema.org",
          "@type" => "BlogPosting",
          "headline" => get_the_title(),
          "image" => $image,
          "datePublished" => get_the_date('c'),
          "dateModified" => get_the_modified_date('c'),
          "author" => [
            "@type" => "Person",
            "name" => $author_name,
            "url" => $author_url
          ],
          "publisher" => [
            "@type" => "Organization",
            "name" => get_bloginfo('name'),
            "logo" => [
              "@type" => "ImageObject",
              "url" => get_stylesheet_directory_uri() . '/logo.png'
            ]
          ],
          "mainEntityOfPage" => get_permalink(),
          "url" => get_permalink(),
          "description" => get_the_excerpt()
        ];
        $jsonld = remove_empty($jsonld);
        echo '<script type="application/ld+json">' . json_encode($jsonld, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
      } elseif (is_page()) {
        // Statische pagina's als WebPage
        $image = get_the_post_thumbnail_url(get_the_ID(), 'full');
        $jsonld = [
          "@context" => "https://schema.org",
          "@type" => "WebPage",
          "name" => get_the_title(),
          "url" => get_permalink(),
          "description" => get_the_excerpt(),
          "image" => $image,
          "publisher" => [
            "@type" => "Organization",
            "name" => get_bloginfo('name'),
            "logo" => [
              "@type" => "ImageObject",
              "url" => get_stylesheet_directory_uri() . '/logo.png'
            ]
          ]
        ];
        $jsonld = remove_empty($jsonld);
        echo '<script type="application/ld+json">' . json_encode($jsonld, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
      }
      ?>