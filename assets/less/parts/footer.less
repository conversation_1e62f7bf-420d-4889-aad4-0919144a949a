@import '../vw_values.less';
@import '../constants.less';

body {
  &.touch {
    
  }
}

.footer {
    font-size: @vw18;
    background: @primaryColor;
    padding: @vw70 0 @vw20 0;
    color: @hardWhite;
    position: relative;
    overflow: hidden;
    &:after {
      z-index: 1;
    }
    a {
      color: @hardWhite;
      text-decoration: none;
      .transitionMore(opacity, .3s);
    }
    .footerTitle {
      text-transform: uppercase;
      font-size: @vw20;
      color: rgba(255, 255, 255, 0.4);
      margin-bottom: @vw20;
      font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
      i {
        color: @secondaryColorLight;
      }
    }
    p {
      font-size: @vw18;
    }
    &.inview {
      .divider {
        .transform(scaleX(1));
        .transitionMore(transform, .6s, .45s, cubic-bezier(0.85, 0, 0.15, 1));
      }
    }
    .artist {
      .transitionMore(opacity, .3s);
      &:hover {
          opacity: .5;
      }
    }
    .backgroundImage {
      position: absolute;
      top: 0%;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
      .transform(translate3d(0,0,0));
      -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
      mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
      &:before {
        height: 50%;
        position: absolute;
        top: 0;
        width: 100%;
        left: 0;
        pointer-events: none;
        z-index: 2;
        content: '';
        background: (linear-gradient(0deg,rgba(8,0,54,0), @primaryColor));
      }
      &:after {
        height: 50%;
        position: absolute;
        top: auto;
        bottom: 0;
        width: 100%;
        left: 0;
        pointer-events: none;
        z-index: 2;
        content: '';
        background: (linear-gradient(180deg,rgba(8,0,54,0), @primaryColor));
      }
      img {
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%,-50%) scale(1));
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
    .socials {
      margin-top: @vw40;
      .social {
        display: inline-block;
        height: @vw46;
        width: @vw46;
        cursor: pointer;
        .rounded(50%);
        background: rgba(255,255,255,.1);
        text-decoration: none;
        color: @hardWhite;
        line-height: @vw49;
        -webkit-transition: color .3s, background-color .3s;
        transition: color .3s, background-color .3s;
        text-align: center;
        font-size: @vw22;
        &:not(:last-child) {
          margin-right: @vw20;
        }
        &:hover {
          background-color: rgba(255,255,255, 1);
          color: @primaryColor;
        }
        i {
          pointer-events: none;
        }
      }
    }
    .logo {
      .transitionMore(opacity, .3s);
      &:hover {
        opacity: .5;
      }
      img {
        display: inline-block;
        height: @vw30;
        width: auto;
        margin-bottom: @vw70;
        object-fit: contain;
      }
    }
    ul {
      line-height: 1.5;
      list-style: none;
      li {
        a {
          color: @hardWhite;
          font-size: @vw18;
          text-decoration: none;
          cursor: pointer;
          .transitionMore(color, .3s);
          &:hover {
            color: @secondaryColorLight;
          }
        }
      }
    }
    .link {
      color: @secondaryColorLight;
      font-size: @vw18;
      display: inline-block;
      text-transform: lowercase;
      text-decoration: none;
      line-height: 1.4;
      cursor: pointer;
      position: relative;
      &:hover {
        &:before, &:after {
          .transitionMore(transform, .3s, 0s, cubic-bezier(0.87, 0, 0.13, 1));
        }
        &:before {
          .transform(scaleX(0));
          transition-delay: 0s;
        }
        &:after {
          .transform(scaleX(1));
          transition-delay: .15s;
        }
      }
      &:before, &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: auto;
        transform-origin: right;
        right: 0;
        background: @secondaryColorLight;
        .transform(scaleX(1));
        height: 1px;
        width: 100%;
      }
      &:after {
        left: 0;
        right: auto;
        transform-origin: left;
        .transform(scaleX(0));
      }
      &:not(:last-child) {
        margin-right: @vw5;
      }
    }
    .topFooter {
      .cols {
        .col {
          &:first-child {
            width: calc(50% ~"-" @vw16);
          }
        }
      }
    }
    .footerContent {
      position: relative;
      .imageCursor {
        position: absolute;
        height: @vw100 + @vw18;
        width: @vw100 + @vw100 + @vw28;
        top: 0;
        left: 0;
        pointer-events: none;
        opacity: 0;
        overflow: hidden;
        img {
          top: 0;
          left: 0;
          object-fit: cover;
          object-position: center;
          position: absolute;
          width: 100%;
          height: 100%;
        }
      }
    }
    .cols {
        .col {
          display: inline-block;
          vertical-align: top;
          margin: 0 @vw8;
          padding-right: @vw106 + @vw16;
          width: calc(25% ~"-" @vw16);
        }
      }
    // .backgroundWrapper { 
    //   width: 60vw;
    //   position: absolute;
    //   height: 60vw;
    //   .transform(translateY(-50%));
    //   top: 50%;
    //   opacity: 1;
    //   left: 0;
    //   .background {
    //     opacity: 1;
    //     position: absolute;
    //     animation: moveBackground 10s infinite ease-in-out alternate;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     .rounded(50%);
    //     height: 100%; 
    //     background: @hardWhite;
    //     -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    //     mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    //   }
    // }
    .bottomFooter {
        color: @hardBlack;
        margin-top: @vw22;
        .col {
          display: inline-block;
          vertical-align: middle;
          width: 50%;
          &:last-child {
            text-align: right;
          }
        }
        .menu {
          opacity: .7;
          li {
            display: inline-block;
          }
          a {
              display: inline-block;
              vertical-align: middle;
              padding: @vw10;
              cursor: pointer;
              color: @hardWhite;
              text-decoration: none;
              .transitionMore(opacity, .3s);
              &:not(:last-of-type) {
                  margin-right: @vw22;
              }
              &:hover {
                color: @hardWhite;
                opacity: .5;
              }
          }
    }
    .logo {
      width: @vw24;
      display: inline-block;
      vertical-align: middle;
      svg {
        object-fit: contain;
        width: 100%;
        height: auto;
      }
    }
  }
  .divider {
    margin: @vw44 0;
    .transform(scaleX(0));
    transform-origin: left;
    height: 1px;
    background: @secondaryColorLight;
  }
  .middleFooter {
    .cols {
      .col {
        padding-right: @vw40;
      }
    }
  }
}

@media all and (max-width: 1160px) {
    .footer {
      padding: @vw70-1160 0 @vw20-1160 0;
      .cols {
        margin-left: -@vw20-1160;
        width: calc(100% + @vw40-1160);
        .col {
          margin: 0 @vw20-1160;
          width: calc(33.3333% - @vw40-1160);
        }
      }
      img {
        width: @vw100-1160 + @vw50-1160;
        margin-top: @vw40-1160;
      }
      .bigTitle {
        a {
          padding-right: @vw45-1160;
          padding-bottom: @vw10-1160;
          padding-left: 0;
          &:after {
            height: 2px;
          }
          &:hover {
            padding-left: @vw45-1160;
          }
          i {
            left: -@vw30-1160;
          }
        }
      }
      .logo {
        width: @vw24-1160;
      }
      .innerMenu {
        margin-left: @vw80-1160;
        a {
            padding: @vw10-1160;
            &:not(:last-of-type) {
                margin-right: @vw22-1160;
            }
        }
    }
      .bottomFooter {
        margin-top: @vw22-1160;
      }
    }
  }
  
  @media all and (max-width: 580px) {
    .footer {
      padding: @vw70-580 0 @vw20-580 0;
      .backgroundWrapper { 
        width: 120vw;
        height: 120vw;
      }
      .cols {
        margin-left: -@vw20-580;
        width: calc(100% + @vw40-580);
        .col {
          margin: 0 @vw20-580;
          width: calc(50% - @vw40-580);
          &:nth-child(2) {
            display: none;
          }
        }
      }
      img {
        width: @vw100-580 + @vw50-580;
        margin-top: @vw40-580;
      }
      .bigTitle {
        a {
          padding-right: @vw45-580;
          padding-bottom: @vw10-580;
          padding-left: 0;
          &:after {
            height: 2px;
          }
          &:hover {
            padding-left: @vw45-580;
          }
          i {
            left: -@vw30-580;
          }
        }
      }
      .logo {
        width: @vw24-580;
      }
      .bottomFooter {
        margin-top: @vw50-580;
        font-size: @vw22-580;
      }
      .innerMenu {
          display: none;
      }
    }
  }
  
  @keyframes moveBackground {
    0% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(10vw, -5vw);
    }
    50% {
      transform: translate(-10vw, 5vw);
    }
    75% {
      transform: translate(5vw, -10vw);
    }
    100% {
      transform: translate(0, 0);
    }
  }
