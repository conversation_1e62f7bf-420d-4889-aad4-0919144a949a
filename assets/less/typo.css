.hugeTitle.white,
.bigTitle.white,
.biggerTitle.white,
.normalTitle.white,
.smallTitle.white,
.subTitle.white,
.tinyTitle.white {
  color: #FFFFFF;
}
.hugeTitle .white,
.bigTitle .white,
.biggerTitle .white,
.normalTitle .white,
.smallTitle .white,
.subTitle .white,
.tinyTitle .white {
  color: #FFFFFF;
}
.hugeTitle {
  font-size: 5.787vw;
  text-decoration: none;
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 800;
  font-style: normal;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.hugeTitle.link {
  cursor: pointer;
  color: #080036;
}
.hugeTitle.link:hover {
  opacity: 0.6;
}
.hugeTitle.link span {
  cursor: pointer;
}
.bigTitle {
  font-size: 6.25vw;
  letter-spacing: -6px;
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
}
.bigTitle.compact {
  font-size: 7.523vw;
  text-transform: uppercase;
  letter-spacing: 0;
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 800;
  font-style: normal;
}
.mediumTitle {
  font-size: 3.472vw;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}
.normalTitle {
  font-size: 2.315vw;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}
.smallTitle {
  font-size: 1.736vw;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}
.subTitle {
  font-size: 1.389vw;
  line-height: 1.4;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 500;
  font-style: normal;
}
.subTitle.primary {
  color: #080036;
}
.subTitle.secondary {
  color: #5A51A3;
}
.tinyTitle {
  font-size: 1.157vw;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 800;
  font-style: normal;
}
.tinyTitle.smaller {
  font-size: 0.984vw;
}
.text.bigger {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.bigger p {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.white p {
  color: #FFFFFF;
}
.text:not(:first-child) {
  margin-top: 1.157vw;
}
.text p:not(:last-child) {
  margin-bottom: 1.736vw;
}
@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: 6.207vw;
  }
  .bigTitle {
    font-size: 7.069vw;
  }
  .bigTitle.compact {
    font-size: 6.034vw;
  }
  .mediumTitle {
    font-size: 4.31vw;
  }
  .subTitle {
    font-size: 2.069vw;
  }
  .tinyTitle {
    font-size: 1.379vw;
  }
  .text.bigger {
    font-size: 1.897vw;
  }
  .text.bigger p {
    font-size: 1.897vw;
  }
  .text:not(:first-child) {
    margin-top: 1.724vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 6.034vw;
  }
  .bigTitle {
    font-size: 12.069vw;
  }
  .bigTitle.compact {
    font-size: 12.069vw;
  }
  .mediumTitle {
    font-size: 8.62vw;
  }
  .subTitle {
    font-size: 4.137vw;
  }
  .tinyTitle {
    font-size: 2.758vw;
  }
  .text.bigger {
    font-size: 3.793vw;
  }
  .text.bigger p {
    font-size: 3.793vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
