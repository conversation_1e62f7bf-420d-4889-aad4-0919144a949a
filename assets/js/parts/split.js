document.fonts.ready.then(function () {
    $(document).on("initPage", function() {
        splitEach();
    });
});

function splitEach () {
    $("[data-lines]").each(function(i, el) {
        var splitThis = $(el);
        new SplitText(splitThis, { type: "lines", linesClass: "line" });
    });
    $("[data-words]").each(function(i, el) {
        var splitThis = $(el);
        new SplitText(splitThis, { type: "words", wordsClass: "word" });
    });
}