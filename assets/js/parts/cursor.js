document.fonts.ready.then(function () {
  $(document).on("initPage", function () {
    if (!$("body.touch").length > 0) {
        cursorInit();
    }
  });
});

function cursorInit() {
    const $cursor = $(".mainCursor");
    let mouseX = 0;
    let mouseY = 0;
    let currentX = 0;
    let currentY = 0;
    let lastX = 0;
    let lastY = 0;
    let scale = 1;

    $(window).on("mousemove", function (e) {
        mouseX = e.pageX;
        mouseY = e.pageY - $(window).scrollTop();
    });

    $(document).on("mouseenter", "[data-show-cursor]", function () {
        $cursor.addClass("show");
    }).on("mouseleave", "[data-show-cursor]", function () {
        $cursor.removeClass("show");
    });

    function render() {
        const dx = mouseX - lastX;
        const dy = mouseY - lastY;
        const velocity = Math.sqrt(dx * dx + dy * dy);

        // Clamp scale so it doesn’t get too big
        const maxScale = 1.5;
        const minScale = 1;
        const newScale = Math.min(maxScale, minScale + velocity / 100);

        scale += (newScale - scale) * 0.2;

        currentX += (mouseX - currentX) * 0.2;
        currentY += (mouseY - currentY) * 0.2;

        gsap.set($cursor, {
            x: currentX,
            y: currentY,
            scaleX: scale,
            scaleY: scale
        });

        lastX = mouseX;
        lastY = mouseY;

        requestAnimationFrame(render);
    }

    render();
}
