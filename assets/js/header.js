// Use Promise.race to set a timeout for font loading
Promise.race([
  document.fonts.ready,
  new Promise(resolve => setTimeout(resolve, 2000)) // 2 second timeout
]).then(function () {
  $(document).on("initPage", function () {
    setHeaderBackground();
  });
});

function setHeaderBackground() {
  // Clear any existing ScrollTriggers for header background
  ScrollTrigger.getAll().forEach(trigger => {
    if (trigger.vars && trigger.vars.id === 'headerBackground') {
      trigger.kill();
    }
  });

  const $header = $("header");
  const $sections = $("section");

  // Check if first section has whiteBackground class on page load
  const $firstSection = $sections.first();
  if ($firstSection.hasClass('whiteBackground')) {
    $header.addClass('showBackground');
  } else {
    $header.removeClass('showBackground');
  }

  // Create ScrollTrigger for each section to monitor which one is in view
  $sections.each(function(index) {
    const section = this;
    const $section = $(section);

    ScrollTrigger.create({
      id: 'headerBackground',
      trigger: section,
      start: "top center",
      end: "bottom center",
      onEnter: () => {
        if ($section.hasClass('whiteBackground')) {
          $header.addClass('showBackground');
        } else {
          $header.removeClass('showBackground');
        }
      },
      onEnterBack: () => {
        if ($section.hasClass('whiteBackground')) {
          $header.addClass('showBackground');
        } else {
          $header.removeClass('showBackground');
        }
      },
      onLeave: () => {
        // Check what the next section will be
        const $nextSection = $section.next('section');
        if ($nextSection.length) {
          if ($nextSection.hasClass('whiteBackground')) {
            $header.addClass('showBackground');
          } else {
            $header.removeClass('showBackground');
          }
        } else {
          // No next section, remove background
          $header.removeClass('showBackground');
        }
      },
      onLeaveBack: () => {
        // Check what the previous section will be
        const $prevSection = $section.prev('section');
        if ($prevSection.length) {
          if ($prevSection.hasClass('whiteBackground')) {
            $header.addClass('showBackground');
          } else {
            $header.removeClass('showBackground');
          }
        } else {
          // No previous section, remove background
          $header.removeClass('showBackground');
        }
      }
    });
  });
}
