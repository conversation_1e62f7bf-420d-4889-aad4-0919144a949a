document.fonts.ready.then(function () {
  $(document).on("initPage", () => {
    setFooter();
  });
});

function setFooter() {
  $(document).find("footer .footerContent.office").each(function(i, el) {
    var imageCursor = $(el).find(".imageCursor"); // Zorg ervoor dat imageCursor het juiste element is
    var container = $(el);

    // Voeg een enkele mousemove eventlistener toe aan de container
    $(container).on("mousemove", function(e) {
      var relX = e.pageX - $(container).offset().left;
      var relY = e.pageY - $(container).offset().top;
      gsap.to(imageCursor, {
        duration: 0.3,
        x: relX,
        y: relY
      });
      gsap.to(imageCursor.find("img"), {
        duration: 0.3,
        skewX: relX / 10,
        scale: 1 + relX / 1000
      });
    })
    .on("mouseenter", function() {
      gsap.to(imageCursor, { duration: 0.4, scale: 1, autoAlpha: 1 });
    })
    .on("mouseleave", function() {
      gsap.to(imageCursor, { duration: 0.3, scale: 0, autoAlpha: 0 });
    });
  });
}
