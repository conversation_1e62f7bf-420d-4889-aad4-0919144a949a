var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

// Use Promise.race to set a timeout for font loading
Promise.race([
  document.fonts.ready,
  new Promise(resolve => setTimeout(resolve, 1000)) // 1 second timeout
]).then(function () {

  function isTouchDevice() {
    return ('ontouchstart' in window || navigator.maxTouchPoints > 0);
  }

  if (isTouchDevice()) {
    $('body').addClass('touch');
  }

  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }

  updateDynamicScriptsArray();


  pageContainerWrap = new Swup({
    cache:true,
    containers: ["#pageContainer"],
    animateHistoryBrowsing: true,
    plugins: [new SwupHeadPlugin({
      persistAssets: true,
      persistTags: 'style link',
    }), new SwupGtagPlugin({
      gaMeasurementId: '',
    })]
  });

  pageContainerWrap.on('clickLink', () => {
    scrollValues[window.location.href] = window.scrollY;
  });

  pageContainerWrap.on('popState', () => {
    popState = true;
    $(document).on("initPage", function(){
      if(popState){
        window.scrollTo(0, scrollValues[window.location.href]);
        popState = false;
      }
    });
  });

  containerWidth = $(window).width();

  setTimeout(function(){
    preloadPage();
  }, 100);

  pageContainerWrap.on('willReplaceContent', () => {
    inlineStyles = $("head style");
  });


  pageContainerWrap.on('pageView', () => {
    dynamicScriptLoad();
    updateDynamicScriptsArray();
    if (inlineStyles) {
      $("head").append($(inlineStyles));
      inlineStyles = null;
    }
    setTimeout(function(){
      initPage();
    }, 100);
  });

  pageContainerWrap.on('animationOutDone', () => {
    scroller.scrollTo(0,{offset: 0, duration:0, easing: "linear", immediate: true});
    $("header").removeClass("scrolled scrollDown");
    scroller.stop();
    scroller.start();
    $("html").addClass("stopScroll");
  });

  pageContainerWrap.on('willReplaceContent', () => {
    $("html").addClass("stopScroll");
  });
});
function updateDynamicScriptsArray(){
  $("head script").each(function(i, el){
    if($.inArray($(el).attr("src"), dynamicScripts) == -1){
      dynamicScripts.push($(el).attr("src"));
    }
  });
}

function dynamicScriptLoad(){
  $("head script").each(function(i, el){
    if($.inArray($(el).attr("src"), dynamicScripts) == -1){
      let scriptEle = document.createElement("script");
      scriptEle.setAttribute("src", $(el).attr("src"));
      $(el).remove();
      document.head.appendChild(scriptEle);
    }
  });
  var container = $("#pageContainer")[0];
  var arr = container.getElementsByTagName('script');
}

function initLenis(){
  scroller = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
    orientation: 'vertical', // vertical, horizontal
    gestureOrientation: 'vertical', // vertical, horizontal, both
    smoothWheel: true,
    smoothTouch: false,
  });
  scroller.on("scroll", function(e){
    if(currentScrollY > 0){
      $("header").addClass("scrolled");
    } else {
      $("header").removeClass("scrolled");
    }
    if(e.direction == 1){
      $("header").addClass("scrollDown");
    } else if(e.direction == -1) {
      $("header").removeClass("scrollDown");
    }
    // if (!menuDisabled && menuIsOpen) {
    //   closeMenu($("header #menu"));
    // }
    var setHeaderColorDark = false;
    $(".changeHeaderToDark").each(function(i,el){
      if($(el).offset().top - $("header .innerBar .right a").offset().top - ($("header .innerBar .right a").height() / 2) <= 0 && $(el).offset().top - $("header .innerBar .right a").offset().top - ($("header .innerBar .right a").height() / 2) >= -Math.abs($(el).outerHeight())){
        setHeaderColorDark = true;
      }
    });
    if(setHeaderColorDark){
      $("header").addClass("dark");
    } else {
      $("header").removeClass("dark");
    }
  });
}

function raf(time) {
  scroller.raf(time);
  requestAnimationFrame(raf);
}

function preloadPage(){
  initLenis();
  initPage();
  currentScrollY = $(window).scrollTop();
  scroller.on("scroll", function(e){
    currentScrollY = $(window).scrollTop();
  });

  requestAnimationFrame(raf);

  // TweenMax.to('.noise', .03, {repeat:-1, onRepeat: repeatStatic, ease:SteppedEase.config(1)});
  // function repeatStatic(){
  //   TweenMax.set('.noise', {backgroundPosition: Math.floor(Math.random() * 100) + 1 + "% " + Math.floor(Math.random() * 10) + 1 + "%"});
  // }

  setTimeout(function(){
    $("html, body").removeClass("overflow");
    $(".content").removeClass("fade");
    $("header").addClass("active");
  }, 300);
}

function initPage(){

  $("html").removeClass("stopScroll fade");

  $(document).trigger("initPage");

  if ($(".instagramBlock").length > 0) {
    sbi_init();
  }

  lazyLoadImages();
  checkInviewClasses();
  setFooter();

}

function checkInviewClasses() {
  $("[data-init]").each(function() {
    const scrollDirect = $(this).data("scroll-direct");
    ScrollTrigger.create({
      trigger: this,
      start: scrollDirect ? "0% 100%" : "0% 90%",
      end: "0% 90%",
      once: true,
      onEnter: () => {
        if ($(this).get(0).hasAttribute("data-split") && !$(this).hasClass("inview")) {
          splitLines($(this));
        }
        if ($(this).get(0).hasAttribute("data-init-delay") && !$(this).hasClass("inview")) {
          var item = $(this);
          setTimeout(function() {
            $(item).addClass("inview");
            return false;
          }, $(this).data("init-delay"));
        } else {
          $(this).addClass("inview");
        }
      }
    });
  });
}

function lazyLoadImages() {
  var lazyloadImages;
  if ("IntersectionObserver" in window) {
    lazyloadImages = document.querySelectorAll(".lazy, .lazyload");
    const config = {
      root: null, 
      rootMargin: '1000px',
      threshold: 0.0
    };
    var imageObserver = new IntersectionObserver(function(entries, observer) {
      $(entries).each(function(i, entry) {
        if (entry.isIntersecting) {
          var image = entry.target;
          image.classList.remove("lazy");
          image.src = $(image).data('src');
          imageObserver.unobserve(image);
        }
      });
    },config);
    $(lazyloadImages).each(function(i, image) {
      imageObserver.observe(image);
    });
  } else {
    $(".lazy").each(function(i, image) {
      image.classList.remove("lazy");
      image.src = $(image).data('src');
    });
  }
}