<?php
  $artists = new WP_Query([
    'post_type' => 'artist',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'post_status'    => 'publish',
  ]);
?>
<?php wp_footer(); ?>
</div>
<?php $year = (new DateTime)->format("Y"); ?>
</div>
</div>
<!-- <div class="noise" style="background:url('<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-noise'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title') ?>');"></div> -->
<footer class="footer" data-init data-hide-cursor>
  <div class="backgroundImage">
    <?php if(get_theme_mod('customTheme-main-callout-footer-image')): ?>
      <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-footer-image'))); ?>" alt="Limitless footer image" />
    <?php endif; ?>
  </div>
  <div class="contentWrapper">
    <a class="logo" href="/" title="<?php the_title(); ?>">
        <img class="lazy" data-src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo-white'))); ?>" alt="<?php the_title(); ?>"/>
    </a>
    <span class="backgroundWrapper"><span class="background" data-parallax data-scroll-position="horizontal" data-parallax-speed="4"></span></span>
    <div class="topFooter">
      <div class="cols">
        <div class="col">
          <h3 class="footerTitle"><i class="icon-flame"></i>&nbsp;Our Artists</h3>
          <?php if( $artists->have_posts() ): ?>
            <?php foreach ( $artists->posts as $post ) :
              setup_postdata($post);
              $title = get_the_title($post->ID);
              $link = get_the_permalink($post->ID);
              ?>
              <a class="link artist" title="<?php echo esc_html( $title ); ?>" href="<?php echo esc_url($link); ?>">
                <span class="innerText"><?php echo esc_html( $title ); ?></span>
              </a><?php if ($post !== end($artists->posts)) : ?>, <?php endif; ?>
            <?php endforeach; ?>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>
        </div>
        <div class="col">
          <h3 class="footerTitle"><?php echo get_theme_mod('customTheme-main-callout-title') ?></h3>
          <?php wp_nav_menu( array(
            'menu' => 'primary-footer-menu',
          ) ); ?>
        </div>
        <div class="col">
            <h3 class="footerTitle">About</h3>
            <?php wp_nav_menu( array(
            'menu' => 'secondary-footer-menu',
          ) ); ?>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="middleFooter">
      <div class="cols">
        <div class="col">
          <h3 class="footerTitle">Contact us</h3>
          <div class="footerContent">
            <?php echo wpautop(get_theme_mod('customTheme-main-callout-contact')); ?>
          </div>
          <div class="socials">
            <?php if(get_theme_mod('customTheme-main-callout-instagram')): ?>
              <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-instagram"></i></a>
            <?php endif;
            if(get_theme_mod('customTheme-main-callout-tiktok')): ?>
              <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a>
            <?php endif;
            if(get_theme_mod('customTheme-main-callout-linkedin')): ?>
              <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin', 'https://www.linkedin.com')); ?>" title="linkedin" target="_blank"><i class="icon-linkedin"></i></a>
            <?php endif;
            if(get_theme_mod('customTheme-main-callout-facebook')): ?>
              <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook', 'https://www.facebook.com')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a>
            <?php endif; ?>
          </div>
        </div>
        <div class="col">
          <div class="footerTitle">Office address</div>
          <div class="footerContent office">
            <?php echo wpautop(get_theme_mod('customTheme-main-callout-office-address')); ?>
            <?php if(get_theme_mod('customTheme-main-callout-office-address-image')): ?>
              <div class="imageCursor">
                <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-office-address-image'))); ?>" alt="Office address image" />
              </div>
            <?php endif; ?>
          </div>
        </div>
        <div class="col">
          <h3 class="footerTitle">Invoice address</h3>
          <div class="footerContent office">
            <?php echo wpautop(get_theme_mod('customTheme-main-callout-invoice-address')); ?>
            <?php if(get_theme_mod('customTheme-main-callout-invoice-address-image')): ?>
              <div class="imageCursor">
                <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-invoice-address-image'))); ?>" alt="Invoice address image" />
              </div>
            <?php endif; ?>
          </div>
        </div>
        <div class="col">
          <h3 class="footerTitle">Banking details</h3>
          <div class="footerContent">
            <?php echo wpautop(get_theme_mod('customTheme-main-callout-banking-details')); ?>
          </div>
        </div>
      </div>
    </div>
    <div class="bottomFooter">
      <div class="col">
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                'menu' => 'bottom-footer-menu',
              ) ); ?>
          </div>
    </div>
      <div class="col">
        <div class="signatureDD">
          <a class="linkDD" href="https://www.doordennis.nl" title="Door Dennis" target="_blank">
            <span class="innerTextDD">Website</span>
            <span class="svgWrap">
              <svg width="71.522" height="11.231" viewBox="0 0 71.522 11.231">
                <defs>
                  <clipPath>
                    <rect width="71.522" height="11.232" fill="none"/>
                  </clipPath>
                </defs>
                <g transform="translate(-771 -850)">
                  <g transform="translate(771 850)">
                    <g transform="translate(0 0)" clip-path="url(#clip-path)">
                      <path d="M22.767,22.25a.2.2,0,0,0-.17.1,2.564,2.564,0,0,1-2.174,1.207H10.36a.006.006,0,0,1-.006-.006V22.42a.191.191,0,0,0-.2-.19,1.7,1.7,0,0,0-1.609,1.338l0,0V24.72a.186.186,0,0,0,.186.186l6.443,0h5.25a3.922,3.922,0,0,0,3.6-2.376.2.2,0,0,0-.185-.277Z" transform="translate(-5.289 -13.764)" fill="#161615"/>
                      <path d="M18.873.2H3.936a.2.2,0,0,0-.2.2v.959a.2.2,0,0,0,.2.2h.436C6.982,1.63,7,2.227,7,3.421a.006.006,0,0,1-.006.006H.2a.2.2,0,0,0-.2.2v.959a.2.2,0,0,0,.2.2h.26c2.809.058,2.8.66,2.8,1.906a.006.006,0,0,1-.006.006H2.386A2.324,2.324,0,0,0,.025,9.17a2.3,2.3,0,0,0,2.29,2.113h.173a.2.2,0,0,0,.2-.2v-.959a.2.2,0,0,0-.2-.2H2.315a.947.947,0,0,1,0-1.894H17.6l1.386,0h0A3.919,3.919,0,0,0,18.873.2M9.883,6.689H8.811A.006.006,0,0,1,8.8,6.683V5.521a.191.191,0,0,0-.2-.19A1.6,1.6,0,0,0,7,6.689V6.683a.006.006,0,0,1-.006.006H5.071a.006.006,0,0,1-.006-.006V5.974c0-.879-.1-1.16.437-1.2h9.631a2.571,2.571,0,0,1,2.478,***********,0,0,1-.006.008ZM19,6.683a.007.007,0,0,1-.006,0,3.924,3.924,0,0,0-3.861-3.251H8.811A.006.006,0,0,1,8.8,3.421V2.745c0-.853-.1-1.143.392-1.193h9.611a2.611,2.611,0,0,1,2.63,2.434A2.569,2.569,0,0,1,19,6.683" transform="translate(0 -0.125)" fill="#161615"/>
                      <path d="M72.556,5.345V.234h2.853a4.062,4.062,0,0,1,1.627.3,2.28,2.28,0,0,1,1.047.869,2.535,2.535,0,0,1,.364,1.389,2.532,2.532,0,0,1-.364,1.393,2.287,2.287,0,0,1-1.047.865,4.062,4.062,0,0,1-1.627.3Zm1.508-1.122H75.32a2.07,2.07,0,0,0,.642-.093,1.5,1.5,0,0,0,.494-.264,1.164,1.164,0,0,0,.323-.423,1.362,1.362,0,0,0,.115-.572V2.7a1.337,1.337,0,0,0-.115-.568,1.177,1.177,0,0,0-.323-.42,1.494,1.494,0,0,0-.494-.264,2.07,2.07,0,0,0-.642-.093H74.064Z" transform="translate(-44.924 -0.145)" fill="#161615"/>
                      <path d="M93.52,5.289a4.585,4.585,0,0,1-1.779-.316,2.58,2.58,0,0,1-1.163-.906,2.437,2.437,0,0,1-.408-1.422,2.439,2.439,0,0,1,.408-1.422A2.582,2.582,0,0,1,91.741.316a5.2,5.2,0,0,1,3.57,0,2.587,2.587,0,0,1,1.159.906,2.439,2.439,0,0,1,.408,1.422,2.437,2.437,0,0,1-.408,1.422,2.585,2.585,0,0,1-1.159.906,4.6,4.6,0,0,1-1.79.316m0-1.122a2.6,2.6,0,0,0,.732-.1,1.677,1.677,0,0,0,.568-.282,1.27,1.27,0,0,0,.368-.457,1.384,1.384,0,0,0,.13-.605V2.548a1.343,1.343,0,0,0-.13-.594A1.28,1.28,0,0,0,94.82,1.5a1.677,1.677,0,0,0-.568-.283,2.619,2.619,0,0,0-.732-.1,2.574,2.574,0,0,0-.728.1,1.722,1.722,0,0,0-.569.283,1.229,1.229,0,0,0-.368.453,1.374,1.374,0,0,0-.126.594v.178a1.415,1.415,0,0,0,.126.605,1.22,1.22,0,0,0,.368.457,1.722,1.722,0,0,0,.569.282,2.56,2.56,0,0,0,.728.1" transform="translate(-55.829 0)" fill="#161615"/>
                      <path d="M113.278,5.289a4.583,4.583,0,0,1-1.779-.316,2.58,2.58,0,0,1-1.163-.906,2.437,2.437,0,0,1-.408-1.422,2.439,2.439,0,0,1,.408-1.422A2.582,2.582,0,0,1,111.5.316a5.2,5.2,0,0,1,3.569,0,2.579,2.579,0,0,1,1.159.906,2.436,2.436,0,0,1,.409,1.422,2.435,2.435,0,0,1-.409,1.422,2.577,2.577,0,0,1-1.159.906,4.6,4.6,0,0,1-1.79.316m0-1.122a2.6,2.6,0,0,0,.732-.1,1.677,1.677,0,0,0,.568-.282,1.27,1.27,0,0,0,.368-.457,1.383,1.383,0,0,0,.129-.605V2.548a1.343,1.343,0,0,0-.129-.594,1.28,1.28,0,0,0-.368-.453,1.677,1.677,0,0,0-.568-.283,2.619,2.619,0,0,0-.732-.1,2.579,2.579,0,0,0-.728.1,1.724,1.724,0,0,0-.568.283,1.225,1.225,0,0,0-.368.453,1.374,1.374,0,0,0-.126.594v.178a1.415,1.415,0,0,0,.126.605,1.216,1.216,0,0,0,.368.457,1.724,1.724,0,0,0,.568.282,2.565,2.565,0,0,0,.728.1" transform="translate(-68.063 0)" fill="#161615"/>
                      <path d="M130.271,5.345V.234h8.058a1.694,1.694,0,0,1,.921.23,1.49,1.49,0,0,1,.55.606,1.825,1.825,0,0,1,.186.821,1.725,1.725,0,0,1-.23.88,1.6,1.6,0,0,1-.691.628l1.04,1.946H138.4l-.847-1.7h-5.769v1.7Zm1.508-2.815h6.052a.594.594,0,0,0,.439-.163.594.594,0,0,0,.163-.438.616.616,0,0,0-.074-.308.486.486,0,0,0-.208-.2.708.708,0,0,0-.32-.067h-6.052Z" transform="translate(-80.658 -0.145)" fill="#161615"/>
                      <path d="M72.556,20.949V15.838h6.661a4.062,4.062,0,0,1,1.627.3A2.28,2.28,0,0,1,81.891,17a2.839,2.839,0,0,1,0,2.782,2.287,2.287,0,0,1-1.047.865,4.062,4.062,0,0,1-1.627.3Zm1.508-1.122h5.064a2.07,2.07,0,0,0,.642-.093,1.5,1.5,0,0,0,.494-.264,1.164,1.164,0,0,0,.323-.423,1.362,1.362,0,0,0,.115-.572V18.3a1.337,1.337,0,0,0-.115-.568,1.177,1.177,0,0,0-.323-.42,1.494,1.494,0,0,0-.494-.264,2.07,2.07,0,0,0-.642-.093H74.064Z" transform="translate(-44.924 -9.806)" fill="#161615"/>
                      <path d="M100.754,20.949V15.838h9.127V16.96H102.27v.861h7.136v1.085H102.27v.921h7.671v1.122Z" transform="translate(-62.383 -9.806)" fill="#161615"/>
                      <path d="M127.587,20.949V15.838h1.352l2.645,2.385q.067.059.174.163l.223.215c.077.075.142.144.2.208h.067q0-.134,0-.334t0-.357v-2.28h1.427v5.111h-1.33l-2.585-2.332q-.178-.157-.371-.35t-.312-.3h-.059q0,.1,0,.312t0,.461v2.213Z" transform="translate(-78.996 -9.806)" fill="#161615"/>
                      <path d="M146.859,20.949V15.838h1.352l2.645,2.385q.067.059.174.163l.223.215c.077.075.142.144.2.208h.067q0-.134,0-.334t0-.357v-2.28h1.427v5.111h-1.33l-2.585-2.332q-.178-.157-.371-.35t-.312-.3h-.059q0,.1,0,.312t0,.461v2.213Z" transform="translate(-90.929 -9.806)" fill="#161615"/>
                      <rect width="1.508" height="5.111" transform="translate(63.27 6.032)" fill="#161615"/>
                      <path d="M175.709,20.893a8.611,8.611,0,0,1-1.167-.074,3.412,3.412,0,0,1-.951-.26,1.518,1.518,0,0,1-.639-.52,1.461,1.461,0,0,1-.23-.847v-.037a.14.14,0,0,1,.007-.045h1.486a.467.467,0,0,0-.011.059.512.512,0,0,0,0,.067.425.425,0,0,0,.174.36,1.118,1.118,0,0,0,.487.182,4.536,4.536,0,0,0,.735.052q.171,0,.353-.011t.353-.037a1.508,1.508,0,0,0,.308-.078.6.6,0,0,0,.215-.13.267.267,0,0,0,.078-.2.279.279,0,0,0-.167-.249,1.584,1.584,0,0,0-.46-.149q-.294-.055-.654-.1t-.746-.108a5.986,5.986,0,0,1-.75-.171,2.7,2.7,0,0,1-.654-.286,1.361,1.361,0,0,1-.461-.461,1.3,1.3,0,0,1-.171-.683,1.2,1.2,0,0,1,.2-.7,1.561,1.561,0,0,1,.576-.486,3.121,3.121,0,0,1,.876-.286,6.077,6.077,0,0,1,1.1-.093,5.775,5.775,0,0,1,1.1.1,3,3,0,0,1,.847.286,1.459,1.459,0,0,1,.542.475,1.189,1.189,0,0,1,.19.673v.1h-1.464v-.059a.37.37,0,0,0-.134-.278.936.936,0,0,0-.39-.193,2.374,2.374,0,0,0-.62-.07,3.817,3.817,0,0,0-.721.056,1.135,1.135,0,0,0-.416.149.24.24,0,0,0,.037.446,1.729,1.729,0,0,0,.46.137q.29.052.65.1t.751.1a5.933,5.933,0,0,1,.75.163,2.718,2.718,0,0,1,.65.278,1.378,1.378,0,0,1,.461.446,1.228,1.228,0,0,1,.171.665,1.393,1.393,0,0,1-.356,1,2.06,2.06,0,0,1-.977.55,5.39,5.39,0,0,1-1.422.171" transform="translate(-106.943 -9.661)" fill="#161615"/>
                    </g>
                  </g>
                </g>
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>
</div>
</body>
</html>
