document.fonts.ready.then(function () {
    $(document).on("initPage", function () {
        if ($(".artistUpcomingShowsBlock").length > 0) {
            initializeUpcomingShowsBlock();
        }
    });
});

function initializeUpcomingShowsBlock() {
    const $block = $('.artistUpcomingShowsBlock');
    
    // Initialize animations when block comes into view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                $(entry.target).addClass('inview');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -50px 0px'
    });
    
    $block.each(function() {
        observer.observe(this);
    });
    
    // Add hover effects for show items
    $block.find('.showItem').on('mouseenter', function() {
        gsap.to($(this), {
            y: -5,
            duration: 0.3,
            ease: 'power2.out'
        });
    }).on('mouseleave', function() {
        gsap.to($(this), {
            y: 0,
            duration: 0.3,
            ease: 'power2.out'
        });
    });
    
    // Add click tracking for tickets buttons
    $block.find('.ticketsButton').on('click', function(e) {
        const $button = $(this);
        const showDate = $button.closest('.showItem').find('.showDate').text();
        const showLocation = $button.closest('.showItem').find('.showLocation').text();
        
        // Add a subtle animation on click
        gsap.to($button, {
            scale: 0.95,
            duration: 0.1,
            yoyo: true,
            repeat: 1,
            ease: 'power2.inOut'
        });
        
        // Optional: Track analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'ticket_click', {
                'event_category': 'upcoming_shows',
                'event_label': `${showDate} - ${showLocation}`,
                'value': 1
            });
        }
    });
    
    // Add stagger animation for show items
    function animateShowItems() {
        const $items = $block.find('.showItem');
        
        gsap.fromTo($items, {
            y: 30,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            stagger: 0.1,
            ease: 'power2.out',
            delay: 0.4
        });
    }
    
    // Trigger animation when block is in view
    $block.on('inview', animateShowItems);
}
