//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistPopularReleasesBlock {
  display: flex;
  gap: (@vw100 * 2.5);
  margin: (@vw100 * 3) 0;
  .PopularYoutube, .PopularSpotify {
    flex: 1 1 0;
    iframe {
      width: 100%;
      min-height: (@vw100 * 18);
      border-radius: (@vw100 * 0.8);
      box-shadow: 0 2px 12px rgba(0,0,0,0.18);
    }
  }
}
@media (max-width: 900px) {
  .ArtistPopularReleasesBlock {
    flex-direction: column;
    gap: (@vw100 * 1.5);
  }
}
