@import '../../../assets/less/constants.less';
@import '../../../assets/less/vw_values.less';

.artistMediaBlock {
  padding: @vw80 0;
  background: transparent;
  position: relative;
  &.inview {
    .mediaContainer {
      .youtubeContainer, .spotifyContainer {
        opacity: 1;
        .transform(translateY(0));
        transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
        .stagger(2, 0.15s);
      }
    }
  }
  .normalTitle {
    text-align: left; 
    margin-bottom: @vw26;
  }

  .mediaContainer {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @vw40;
    align-items: flex-start;
  }
  .youtubeContainer, .spotifyContainer {
    opacity: 0;
    .transform(translateY(@vw50));
  }
  .youtubeContainer {
    .videoWrapper {
      position: relative;
      width: 100%;
      height: 0;
      padding-bottom: 56.25%; // 16:9 aspect ratio
      overflow: hidden;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
  
  .spotifyContainer {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; // Same 16:9 aspect ratio as YouTube
    overflow: hidden;

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }
  }
  
  .noMediaMessage {
    text-align: center;
    padding: @vw40;

    p {
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: @vw10;

      &:last-child {
        margin-bottom: 0;
      }

      small {
        font-size: @vw14;
        opacity: 0.8;
      }
    }
  }

  .spotify-fallback,
  .youtube-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw15);

    .fallback-message {
      text-align: center;

      p {
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: @vw15;
        font-size: @vw16;

        &:last-of-type {
          margin-bottom: @vw20;
        }

        small {
          font-size: @vw14;
          opacity: 0.8;
        }
      }

      .button {
        display: inline-flex;
        align-items: center;
        gap: @vw10;
      }
    }
  }

  // Responsive adjustments
  @media all and (max-width: 580px) {
    .mediaContainer {
      grid-template-columns: 1fr;
      gap: @vw30;
    }
  }
}
