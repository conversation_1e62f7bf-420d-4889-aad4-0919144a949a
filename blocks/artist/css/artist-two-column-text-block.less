//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistTwoColumnTextBlock {
  &.inview {
    p {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
      .stagger(90, 0.15s);
    }
  }
  p {
    opacity: 0;
    .transform(translateY(@vw30));
  }
  .cols {
    width: calc(100% ~"+" @vw16);
    margin-left: -@vw8;
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      vertical-align: top;
      &:not(:first-child) {
        opacity: .7;
      }
    }
  }
  p {
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}