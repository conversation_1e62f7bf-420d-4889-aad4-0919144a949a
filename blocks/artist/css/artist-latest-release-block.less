//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistLatestReleasblock {
  .cols {
    width: calc(100% ~"+" @vw16);
    margin-left: -@vw8;
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      vertical-align: top;
      &:first-child {
        padding-right: @vw106 + @vw16;
      }
      img {
        width: 100%;
        height: auto;
      }
    }
  }
  .smallTitle {
    margin-bottom: @vw22;
  }
  .button {
    margin-top: @vw55;
  }
}