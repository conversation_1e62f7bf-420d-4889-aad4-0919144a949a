//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.artistHeaderBlock {
  padding-top: (@vw100 * 3);
  &.inview {
    .artistLogo {
      .transform(scale(1));
      .transitionMore(transform, .6s, .15s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
    .socials {
      .social {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.3s 0.75s ease-out, transform 0.3s 0.75s ease-out;
        .stagger(20, 0.05s);
      }
    }
  }
  .artistLogo {
    display: block;
    margin: auto;
    text-align: center;
    width: auto;
    height: @vw90;
    margin-bottom: @vw50;
    .transform(scale(0));
  }
  .ArtistGenre {
    color: #fff;
    font-size: (@vw100 * 1.1);
    letter-spacing: 2px;
    margin-bottom: (@vw100 * 0.5);
  }
  .hugeTitle {
    text-align: center;
  }
  .socials {
    margin-top: @vw50;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: @vw60;
    a {
      cursor: pointer;
      color: @hardWhite;
      text-decoration: none;
      font-size: @vw25;
      .transform(translateY(@vw20));
      opacity: 0;
      i {
        cursor: pointer;
        .transitionMore(color, .3s);
      }
      &:hover {
        i {
          color: @secondaryColorLight;
        }
      }
    }
  }
  .backToArtists {
    margin: @vw72 0 @vw22 0;
  }
  .infoWrapper {
    position: relative;
    .contactDetails {
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: (@vw106 * 3) + (@vw16 * 2);
      background: @primaryColor;
      padding: @vw44 @vw33;
      border: 1px solid @secondaryColorLight;
    }
    .text {
      margin-bottom: @vw40;
    }
    .button {
      width: 100%;
      &:not(:last-child) {
        margin-bottom: @vw10;
      }
    }
  }
  .imageWrapper, .backgroundImage {
    width: 100%;
    height: auto;
    overflow: hidden;
    .innerImage {
      height: 0;
      .paddingRatio(1204,507);
    }
    img {
        position: absolute;
        top: -10%;
        left: 0;
        width: 100%;
        height: 120%;
        object-fit: cover;
        object-position: top;
      }
  }
  .backgroundImage {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 70%;
    overflow: hidden;
    z-index: 0;
    .transform(translate3d(0,0,0));
    -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
    mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
    img {
      .filter(blur(20px));
    }
  }
}
