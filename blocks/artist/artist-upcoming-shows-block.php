<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$bandsintown_id = get_field('bandsintown_id', $artist_id);
$artist_name = get_the_title($artist_id);

// Bepaal welke artist identifier te gebruiken
$artist_identifier = '';
if ($bandsintown_id) {
    // Als het een numeriek ID is, gebruik de artist naam voor de widget
    if (is_numeric($bandsintown_id)) {
        $artist_identifier = $artist_name;
    } else {
        $artist_identifier = $bandsintown_id;
    }
}
if ($artist_identifier): ?>
    <section class="artistUpcomingShowsBlock" data-init data-show-cursor>
        <div class="contentWrapper smaller">
            <h2 class="normalTitle">UPCOMING SHOWS</h2>
            <!-- <div class="bandsintownSimple">
                <div class="showItem">
                    <div class="showTickets">
                        <a href="https://www.bandsintown.com/a/<?= esc_attr($bandsintown_id) ?>" target="_blank" rel="noopener" class="ticketsButton">
                            VIEW SHOWS
                        </a>
                    </div>
                </div>
            </div> -->

            <div class="bandsintownWidget" style="margin-top: 20px;">
                <a class="bit-widget-initializer"
                   data-artist-name="<?= esc_attr($artist_identifier) ?>"
                    data-display-past-dates="false"
                    data-display-limit="12"
                   	data-display-local-dates="false"
                    data-link-color="#00b4b3"
                    data-font="ApexMk2-Regular"
                    data-display-logo="false"
                    data-display-track-button="false"
                    data-display-lineup="false"

                   >
                </a>
            </div>
        </div>
    </section>

    <!-- Load widget script and customize layout -->
    <script>
    (function() {
        if (typeof window.BIT !== 'undefined') return;

        var script = document.createElement('script');
        script.src = 'https://widgetv3.bandsintown.com/main.min.js';
        script.async = true;
        script.onerror = function() {
            console.log('Bandsintown widget failed to load - using fallback link');
        };
        script.onload = function() {
            // Wait for widget to render then customize layout
            setTimeout(customizeWidgetLayout, 1000);
        };
        document.head.appendChild(script);

        function customizeWidgetLayout() {
            const events = document.querySelectorAll('.bit-event');

            events.forEach(function(event) {
                // Restructure each event to match your design
                const dateEl = event.querySelector('.bit-date');
                const venueEl = event.querySelector('.bit-venue');
                const locationEl = event.querySelector('.bit-location');
                const buttonsEl = event.querySelector('.bit-event-buttons');

                if (dateEl && venueEl && locationEl) {
                    // Create new structure
                    const leftSide = document.createElement('div');
                    leftSide.className = 'show-info-left';
                    leftSide.style.cssText = 'display: flex; align-items: center; gap: 40px; flex: 1;';

                    const dateContainer = document.createElement('div');
                    dateContainer.className = 'show-date-container';
                    dateContainer.style.cssText = 'min-width: 120px;';
                    dateContainer.appendChild(dateEl.cloneNode(true));

                    const detailsContainer = document.createElement('div');
                    detailsContainer.className = 'show-details-container';
                    detailsContainer.style.cssText = 'flex: 1;';

                    const locationClone = locationEl.cloneNode(true);
                    locationClone.style.cssText = 'font-family: "ApexMk2-Regular", Arial, sans-serif !important; font-weight: bold !important; color: #00b4b3 !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-bottom: 5px !important;';

                    const venueClone = venueEl.cloneNode(true);
                    venueClone.style.cssText = 'font-family: "ApexMk2-Regular", Arial, sans-serif !important; color: rgba(255, 255, 255, 0.8) !important; text-transform: uppercase !important; letter-spacing: 0.5px !important;';

                    detailsContainer.appendChild(locationClone);
                    detailsContainer.appendChild(venueClone);

                    leftSide.appendChild(dateContainer);
                    leftSide.appendChild(detailsContainer);

                    // Clear event and rebuild
                    event.innerHTML = '';
                    event.appendChild(leftSide);

                    if (buttonsEl) {
                        const rightSide = document.createElement('div');
                        rightSide.className = 'show-tickets-right';
                        rightSide.appendChild(buttonsEl.cloneNode(true));
                        event.appendChild(rightSide);
                    }

                    // Apply final styling
                    event.style.cssText = 'display: flex !important; align-items: center !important; justify-content: space-between !important; background: rgba(255, 255, 255, 0.05) !important; backdrop-filter: blur(10px) !important; border: 1px solid rgba(255, 255, 255, 0.1) !important; border-radius: 15px !important; margin-bottom: 20px !important; padding: 25px 30px !important; transition: all 0.3s ease !important;';
                }
            });
        }
    })();
    </script>


<?php 
endif;
?>