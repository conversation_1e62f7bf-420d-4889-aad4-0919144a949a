<?php
$size = 'full'; 
$video = get_field("video");
$image = get_field("image");
?>
<section class="imageTextBlock white" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper small">
        <div class="cols">
            <div class="col image">
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php if ($video): ?>
                            <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                                <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                            </video>
                        <?php elseif ($image): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col">
                <?php if(get_field("subtitle")): ?><div class="subTitle primary"><?php the_field("subtitle"); ?></div><?php endif; ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
                <div class="text"><?php the_field("text"); ?></div>
            </div>
        </div>
    </div>
</section>
