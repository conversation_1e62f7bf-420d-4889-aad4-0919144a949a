// out: false
.bigHeaderBlock {
    .topCols {
        margin-bottom: @vw90;
        .col {
            display: inline-block;
            vertical-align: top;
            width: 55%;
            &:first-child {
                width: 45%;
            }
        }
    }
    .bigMediaWrapper {
        padding: @vw100 * 2.7 0 @vw100 + @vw40 0;
        position: relative;
        overflow: hidden;
        .background {
            position: absolute;
            top: -10%;
            left: 0;
            width: 100%;
            height: 120%;
            overflow: hidden;
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: @primaryColor;
                opacity: .5;
            }
            video, img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        .subTitle {
            margin-bottom: @vw30;
        }
        .normalTitle {
            &:not(.overlayText) {
                opacity: .4;
            }
            &.overlayText {
                position: absolute;
                pointer-events: none;
                top: 0;
                left: 0;
                .line {
                width: 0%;
                white-space: nowrap;
                }
            }
        }
    }
    .contentWrapper {
        .innerWrapper {
            padding-left: 45%;
        }
    }
    .extraText {
        margin-top: @vw100 + @vw40;
        padding-right: (@vw106 * 2) + (@vw16 * 2);
    }
    .images {
        .imageWrapper {
            display: inline-block;
            vertical-align: bottom;
            &:first-child {
                margin-bottom: @vw100 + @vw80;
                width: (@vw106 * 4) + (@vw16 * 3);
                .innerImage {
                    .paddingRatio(472,577);
                }
            }
            &:last-child {
                width: (@vw106 * 2) + @vw16;
                margin-left: @vw106 + (@vw16 * 2);
                .innerImage {
                    .paddingRatio(228,310);
                }
            }
            .innerImage {
                height: 0;
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
        }
    }
}