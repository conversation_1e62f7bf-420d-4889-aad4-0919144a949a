// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.aboutBlock {
  .light {
    position: absolute;
    left: 0;
    top: 50%;
    .transform(translate(-50%,-50%));
    pointer-events: none;
    width: 70%;
    height: auto;
    opacity: .5;
    background: radial-gradient(rgba(255,255,255,.37), rgba(255,255,255,0));
    mix-blend-mode: hard-light;
    .filter(blur(40px));
    .rounded(50%);
    .innerLight {
      width: 100%;
      height: 0;
      .paddingRatio(1130,726);
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    width: calc(75% ~"- (" (@vw106 * 2) + (@vw16 * 2) ~")");
    &:nth-child(1) {
      width: (@vw106 * 2) + (@vw16 * 2);
    }
    &:nth-child(3) {
      width: 25%;
    }
  }
  .introTextWrapper {
    position: relative;
    display: block;
    .normalTitle {
      &:not(.overlayText) {
        opacity: .4;
      }
      &.overlayText {
        position: absolute;
        pointer-events: none;
        top: 0;
        left: 0;
        .line {
          width: 0%;
          white-space: nowrap;
        }
      }
    }
  }
  .text {
    margin-top: @vw60;
    padding-right: (@vw106 * 2) + (@vw16 * 2);
  }
  .imageWrapper {
      display: inline-block;
      position: relative;
      height: auto;
      overflow: hidden;
      width: 100%;
      vertical-align: top;
      .transform(translate3d(0,0,0));
      &:after {
          opacity: .4;
      }
      .innerImage {
          height: 0;
          .paddingRatio(350,486);
          img, video {
              position: absolute;
              top: -10%;
              left: 0;
              object-fit: cover;
              width: 100%;
              height: 120%;
              object-position: center;
          }
      }
  }
  .buttonWrapper {
    margin-top: @vw55;
    .button {
      &:not(:last-child) {
        margin-right: @vw112;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .textBlock {
  }
}

@media all and (max-width: 580px) {
  .textBlock {
   
  }
}