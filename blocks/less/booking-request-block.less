// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.bookingRequestBlock {
  padding: @vw100 0;
  
  &.inview {
    .formHeader {
      .tinyTitle, .biggerTitle, .text {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
        .stagger(3, 0.1s);
      }
    }
    
    .progressIndicator {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.8s 0.4s ease-out, transform 0.8s 0.4s ease-out;
    }
    
    .formSteps {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 1s 0.6s ease-out, transform 1s 0.6s ease-out;
    }
  }
  
  .contentWrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 @vw25;
  }
  
  .formHeader {
    text-align: center;
    margin-bottom: @vw80;
    
    .tinyTitle, .biggerTitle, .text {
      .transform(translateY(@vw30));
      opacity: 0;
    }
    
    .tinyTitle {
      margin-bottom: @vw15;
    }
    
    .biggerTitle {
      margin-bottom: @vw25;
    }
    
    .text {
      max-width: 600px;
      margin: 0 auto;
    }
  }
  
  .progressIndicator {
    margin-bottom: @vw60;
    .transform(translateY(@vw30));
    opacity: 0;
    
    .progressBar {
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      .rounded(2px);
      margin-bottom: @vw40;
      overflow: hidden;
      
      .progressFill {
        height: 100%;
        background: @secondaryColor;
        width: 0%;
        .rounded(2px);
        transition: width 0.5s ease-out;
      }
    }
    
    .stepIndicators {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .stepIndicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.active {
          .stepNumber {
            background: @secondaryColor;
            color: @hardWhite;
            .transform(scale(1.1));
          }

          .stepLabel {
            color: @secondaryColor;
            font-weight: 600;
          }
        }

        &.completed {
          .stepNumber {
            background: @secondaryColor;
            color: @hardWhite;
            
            &:after {
              content: '✓';
              font-size: 12px;
            }
          }
          
          .stepLabel {
            color: @secondaryColor;
          }
        }
        
        .stepNumber {
          width: 40px;
          height: 40px;
          .rounded(50%);
          background: rgba(255, 255, 255, 0.1);
          color: @hardWhite;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-bottom: @vw10;
          transition: all 0.3s ease;
        }

        .stepLabel {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          text-align: center;
          transition: all 0.3s ease;
        }
      }
    }
  }
  
  .formSteps {
    .transform(translateY(@vw30));
    opacity: 0;
    
    .formStep {
      display: none;
      
      &.active {
        display: block;
      }
      
      .stepTitle {
        font-size: 32px;
        margin-bottom: @vw15;
        color: @hardWhite;
      }
      
      .stepDescription {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: @vw40;
      }
    }
  }
  
  // Artist Selection Styles
  .artistSelection {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: @vw25;
    margin-bottom: @vw40;
    
    .artistOption {
      .artistLabel {
        cursor: pointer;
        display: block;
        
        input[type="checkbox"] {
          display: none;
        }
        
        .artistCard {
          background: rgba(255, 255, 255, 0.05);
          .rounded(@vw15);
          padding: @vw20;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          position: relative;
          overflow: hidden;
          
          &:hover {
            background: rgba(255, 255, 255, 0.08);
            .transform(translateY(-5px));
          }
          
          .artistImage {
            width: 100%;
            height: 200px;
            .rounded(@vw10);
            overflow: hidden;
            margin-bottom: @vw15;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }
          }
          
          .artistInfo {
            .artistName {
              font-size: 18px;
              font-weight: 600;
              color: @hardWhite;
              margin-bottom: @vw5;
            }
            
            .artistType {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.6);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }
          
          .checkmark {
            position: absolute;
            top: @vw15;
            right: @vw15;
            width: 30px;
            height: 30px;
            .rounded(50%);
            background: @secondaryColor;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            .transform(scale(0.5));
            transition: all 0.3s ease;

            &:after {
              content: '✓';
              color: @hardWhite;
              font-size: 16px;
              font-weight: bold;
            }
          }
        }
      }
      
      &.selected {
        .artistCard {
          border-color: @secondaryColor;
          background: rgba(255, 255, 255, 0.1);
          
          .checkmark {
            opacity: 1;
            .transform(scale(1));
          }
          
          .artistImage img {
            .transform(scale(1.05));
          }
        }
      }
    }
  }
  
  // Form Fields Styles
  .formFields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: @vw25;
    
    .fieldGroup {
      &.full-width {
        grid-column: 1 / -1;
      }
      
      &.has-error {
        label {
          color: #ff6b6b;
        }
        
        input, select, textarea {
          border-color: #ff6b6b;
        }
      }
      
      label {
        display: block;
        font-weight: 600;
        color: @hardWhite;
        margin-bottom: @vw10;
        font-size: 16px;
      }

      input, select, textarea {
        width: 100%;
        padding: @vw15 @vw20;
        background: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.1);
        .rounded(@vw10);
        color: @hardWhite;
        font-size: 16px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: @secondaryColor;
          background: rgba(255, 255, 255, 0.08);
        }
        
        &.valid {
          border-color: #51cf66;
        }
        
        &.invalid {
          border-color: #ff6b6b;
        }
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }
      
      select {
        cursor: pointer;
        
        option {
          background: @primaryColor;
          color: @hardWhite;
        }
      }
      
      textarea {
        resize: vertical;
        min-height: 120px;
      }
    }
  }
  
  // Review Summary Styles
  .reviewSummary {
    .summarySection {
      background: rgba(255, 255, 255, 0.05);
      .rounded(@vw15);
      padding: @vw25;
      margin-bottom: @vw25;
      
      h4 {
        font-size: 20px;
        color: @secondaryColor;
        margin-bottom: @vw15;
        font-weight: 600;
      }

      p {
        margin-bottom: @vw10;
        color: rgba(255, 255, 255, 0.9);

        strong {
          color: @hardWhite;
        }
      }
    }
  }
  
  // Navigation Styles
  .formNavigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: @vw50;
    
    .navButton {
      display: flex;
      align-items: center;
      gap: @vw10;
      padding: @vw15 @vw30;
      background: @secondaryColor;
      color: @hardWhite;
      border: none;
      .rounded(@vw10);
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: lighten(@secondaryColor, 10%);
        .transform(translateY(-2px));
      }
      
      &:disabled {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.5);
        cursor: not-allowed;
      }
      
      .arrows {
        display: flex;
        gap: 2px;
        
        i {
          transition: transform 0.3s ease;
        }
      }
      
      &:hover:not(:disabled) {
        .arrows i {
          .transform(translateX(3px));
        }
      }
      
      &.prevButton:hover:not(:disabled) {
        .arrows i {
          .transform(translateX(-3px));
        }
      }
    }
  }
  
  // Validation Error Styles
  .validation-error {
    background: #ff6b6b;
    color: @hardWhite;
    padding: @vw15 @vw20;
    .rounded(@vw10);
    margin-bottom: @vw20;
    font-weight: 600;
    text-align: center;
  }

  // Selected Artists Counter
  .selected-artists-counter {
    text-align: center;
    color: @secondaryColor;
    font-weight: 600;
    margin-top: @vw20;
    padding: @vw10 @vw20;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw10);
    display: inline-block;
  }
  
  // Hidden Contact Form 7
  .hiddenForm {
    display: none;
  }
  
  // Responsive Design
  @media (max-width: 768px) {
    .contentWrapper {
      padding: 0 @vw15;
    }
    
    .progressIndicator {
      .stepIndicators {
        .stepIndicator {
          .stepLabel {
            font-size: 12px;
          }
          
          .stepNumber {
            width: 35px;
            height: 35px;
            font-size: 14px;
          }
        }
      }
    }
    
    .artistSelection {
      grid-template-columns: 1fr;
    }
    
    .formFields {
      grid-template-columns: 1fr;
    }
    
    .formNavigation {
      flex-direction: column;
      gap: @vw15;
      
      .navButton {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
