document.fonts.ready.then(function () {
  $(document).on("initPage", function () {
      if ($(".homeAboutBlock").length > 0){
        initHomeAboutBlock();
      }
  });
});

function initHomeAboutBlock() {
  ScrollTrigger.create({
    trigger: ".homeAboutBlock",
    start: "top top",
    end: "bottom bottom",
    pin: ".homeAboutBlock .stickyItem",
    pinSpacing: true,
  });

  if ($(".homeAboutBlock .stickyItem video").length > 0) {
    gsap.to(".homeAboutBlock .stickyItem video", .6, {
      scale: 1,
      scrollTrigger: {
        trigger: ".homeAboutBlock",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  } else {
    gsap.to(".homeAboutBlock .stickyItem img", .6, {
      scale: 1,
      scrollTrigger: {
        trigger: ".homeAboutBlock",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  }

  // Parallax images
  var parallaxImages = [
    { sel: '.imageRow:nth-of-type(2) .imageWrapper:nth-of-type(1)', y: 60 },
    { sel: '.imageRow:nth-of-type(2) .imageWrapper:nth-of-type(2)', y: 90 },
    { sel: '.imageRow:nth-of-type(3) .imageWrapper:nth-of-type(1)', y: 50 },
    { sel: '.imageRow:nth-of-type(3) .imageWrapper:nth-of-type(2)', y: 20 },
  ];
  parallaxImages.forEach(function(img, i) {
    if($(img.sel).length) {
      gsap.to(img.sel, {
        y: -img.y * 20,
        ease: "none",
        scrollTrigger: {
          trigger: ".homeAboutBlock",
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });
    }
  });
}